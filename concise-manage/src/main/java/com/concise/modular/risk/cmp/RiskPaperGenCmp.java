package com.concise.modular.risk.cmp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.gen.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.modular.risk.riskconfigurationmanagement.entity.RiskConfigurationManagement;
import com.concise.modular.risk.riskconfigurationmanagement.service.RiskConfigurationManagementService;
import com.concise.modular.risk.riskevaluatemanage.entity.RiskEvaluateContext;
import com.concise.modular.risk.risktranscriptmanagement.entity.RiskTranscriptManagement;
import com.concise.modular.risk.risktranscripttemplate.entity.RiskTranscriptTemplate;
import com.concise.modular.risk.risktranscripttemplate.service.RiskTranscriptTemplateService;
import com.concise.paper.entity.PaperMaintenance;
import com.concise.paper.entity.PaperTopic;
import com.concise.paper.param.PaperMaintenanceParam;
import com.concise.paper.service.PaperMaintenanceService;
import com.yomahub.liteflow.core.NodeComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/16
 */
public class RiskPaperGenCmp extends NodeComponent {
    private final Logger log = LoggerFactory.getLogger(RiskPaperGenCmp.class);

    @Override
    public void process() throws Exception {
        RiskEvaluateContext riskEvaluateContext = this.getContextBean(RiskEvaluateContext.class);
        log.info("笔录生成开始,{}", riskEvaluateContext.getCorrectionObjectInformation().getXm());
        if (ObjectUtil.isEmpty(riskEvaluateContext.getEvaluateType())) {
            log.error("评估类型为空");
            return;
        }

        log.info("基础信息：{}", JSON.toJSONString(riskEvaluateContext));
        PaperMaintenanceService paperMaintenanceService = SpringUtil.getBean(PaperMaintenanceService.class);
        //入矫评估笔录
        if ("1".equals(riskEvaluateContext.getEvaluateType())) {
            List<PaperMaintenance> list = paperMaintenanceService.list(new QueryWrapper<PaperMaintenance>().eq("bl_type", "1").eq("paper_type", riskEvaluateContext.getEvaluateType()).eq("del_flag", 0).eq("status", "1").orderByDesc("update_time"));
            if (CollectionUtil.isEmpty(list)) {
                log.warn("未找到符合条件的量卷记录");
            } else {
                log.info("找到符合条件的量卷记录,{}", list.size());
            }
            //笔录
            List<RiskTranscriptManagement> transcriptManagementList = getRiskTranscriptManagements(list, riskEvaluateContext);
            log.info("笔录生成完成,{},{}", riskEvaluateContext.getCorrectionObjectInformation().getXm(), transcriptManagementList.size());
            riskEvaluateContext.setRiskTranscriptManagementList(transcriptManagementList);

        }
        //在矫或者解矫笔录
        if ("2".equals(riskEvaluateContext.getEvaluateType()) || "3".equals(riskEvaluateContext.getEvaluateType())) {
            RiskConfigurationManagement riskConfiguration = riskEvaluateContext.getRiskConfiguration();
            if (ObjectUtil.isEmpty(riskConfiguration)) {
                RiskConfigurationManagementService riskConfigurationManagementService = SpringUtil.getBean(RiskConfigurationManagementService.class);
                riskConfiguration = riskConfigurationManagementService.getLastEnableConfig(riskEvaluateContext.getEvaluateType());
            }
            if (ObjectUtil.isNotEmpty(riskConfiguration)) {
                log.info("找到了配置：{}", riskConfiguration.getConfigName());
                PaperMaintenance paper = paperMaintenanceService.getById(riskConfiguration.getTranscriptId());
                if (ObjectUtil.isNotEmpty(paper)) {
                    List<PaperMaintenance> list = new ArrayList<>();
                    list.add(paper);
                    //笔录
                    List<RiskTranscriptManagement> transcriptManagementList = getRiskTranscriptManagements(list, riskEvaluateContext);
                    log.info("笔录生成完成,{},{}", riskEvaluateContext.getCorrectionObjectInformation().getXm(), transcriptManagementList.size());
                    riskEvaluateContext.setRiskTranscriptManagementList(transcriptManagementList);
                } else {
                    log.error("未找到符合条件的笔录模板");
                }
            } else {
                log.error("未找到符合条件的配置");
            }
        }
        //表单
        List<PaperMaintenance> formPaperList = paperMaintenanceService.list(new QueryWrapper<PaperMaintenance>().eq("paper_type", "4").eq("del_flag", 0).eq("status", "1").orderByDesc("update_time"));
        if (CollectionUtil.isEmpty(formPaperList)) {
            log.warn("未找到符合条件的表单记录");
        }
        if (CollectionUtil.isNotEmpty(formPaperList)) {
            PaperMaintenance paperMaintenance = formPaperList.get(0);
            PaperMaintenanceParam paperMaintenanceParam = new PaperMaintenanceParam();
            paperMaintenanceParam.setId(paperMaintenance.getId());
            PaperMaintenance detail = paperMaintenanceService.detail(paperMaintenanceParam);
            //暂外过滤表单题目
            //1、哺乳暂外对象排除id='1950382768965890049'
            if (riskEvaluateContext.isBreastFeedingWomen()) {
                List<PaperTopic> paperTopics = detail.getTopicList();
                paperTopics.removeIf(paperTopic -> "1950382768965890049".equals(paperTopic.getId()));
                detail.setTopicList(paperTopics);
            }
            //2、其他暂外对象排除id='1950133764092878850'
            if (!riskEvaluateContext.isExecuteOutside()) {
                List<PaperTopic> paperTopics = detail.getTopicList();
                paperTopics.removeIf(paperTopic -> "1950382768999444482".equals(paperTopic.getId()) || "1950382768965890049".equals(paperTopic.getId()));
            }
            riskEvaluateContext.setFormPaper(detail);
            riskEvaluateContext.getRiskEvaluateManage().setFormCollectionJson(JSONObject.toJSONString(detail));
            log.info("表单生成完成,{}", riskEvaluateContext.getCorrectionObjectInformation().getXm());
        }
    }

    private List<RiskTranscriptManagement> getRiskTranscriptManagements(List<PaperMaintenance> list, RiskEvaluateContext riskEvaluateContext) {
        List<RiskTranscriptManagement> transcriptManagementList = new ArrayList<>();
        CorrectionObjectInformation correctionObjectInformation = riskEvaluateContext.getCorrectionObjectInformation();
        PaperMaintenanceService paperMaintenanceService = SpringUtil.getBean(PaperMaintenanceService.class);
        RiskTranscriptTemplateService riskTranscriptTemplateService = SpringUtil.getBean(RiskTranscriptTemplateService.class);
        for (PaperMaintenance paperMaintenance : list) {
            RiskTranscriptManagement riskTranscriptManagement = new RiskTranscriptManagement();
            riskTranscriptManagement.setJzdxId(correctionObjectInformation.getId());
            riskTranscriptManagement.setXm(correctionObjectInformation.getXm());
            riskTranscriptManagement.setJzjg(correctionObjectInformation.getJzjg());
            riskTranscriptManagement.setJzjgName(correctionObjectInformation.getJzjgName());
            riskTranscriptManagement.setTranscriptName(paperMaintenance.getTitle());
            riskTranscriptManagement.setTranscriptType(paperMaintenance.getPaperType());
            riskTranscriptManagement.setPid(riskEvaluateContext.getRiskEvaluateManage().getId());
            riskTranscriptManagement.setDelFlag("0");
            riskTranscriptManagement.setStatus("0");
            //根据笔录类型查找默认的模板
            List<RiskTranscriptTemplate> templateList = riskTranscriptTemplateService.list(new QueryWrapper<RiskTranscriptTemplate>().eq("type", paperMaintenance.getBlType()).eq("del_flag", 0).orderByDesc("update_time"));
            if (CollectionUtil.isNotEmpty(templateList)) {
                riskTranscriptManagement.setTemplateId(templateList.get(0).getId());
            }
            PaperMaintenanceParam paperMaintenanceParam = new PaperMaintenanceParam();
            paperMaintenanceParam.setId(paperMaintenance.getId());
            PaperMaintenance detail = paperMaintenanceService.detail(paperMaintenanceParam);
            if (detail == null) {
                continue;
            }
            riskTranscriptManagement.setTranscriptJson(JSONObject.toJSONString(detail));

            transcriptManagementList.add(riskTranscriptManagement);
        }
        return transcriptManagementList;
    }
}
